{"name": "streaming-db-server", "version": "1.0.0", "description": "Backend API server for Streaming Database website", "main": "index.js", "scripts": {"start": "node index.js", "dev": "nodemon index.js", "test": "jest", "migrate": "node scripts/migrate.js", "seed": "node scripts/seed.js"}, "keywords": ["streaming", "database", "api", "movies", "series"], "author": "Streaming DB", "license": "MIT", "dependencies": {"express": "^4.18.2", "mysql2": "^3.6.5", "bcryptjs": "^2.4.3", "jsonwebtoken": "^9.0.2", "cors": "^2.8.5", "helmet": "^7.1.0", "express-rate-limit": "^7.1.5", "express-validator": "^7.0.1", "multer": "^1.4.5-lts.1", "dotenv": "^16.3.1", "compression": "^1.7.4", "morgan": "^1.10.0", "express-session": "^1.17.3", "connect-mysql": "^3.0.3", "uuid": "^9.0.1", "sharp": "^0.33.0", "csv-parser": "^3.0.0", "csv-writer": "^1.6.0"}, "devDependencies": {"nodemon": "^3.0.2", "jest": "^29.7.0", "supertest": "^6.3.3"}, "engines": {"node": ">=16.0.0"}}